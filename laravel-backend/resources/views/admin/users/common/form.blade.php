
<div class="mb-2"><small>{!! __('index.all_fields_required') !!}</small></div>
<style>
    .is-invalid {
        border-color: red !important;
    }

    .is-invalid + .error-message {
        display: block;
        color: red !important;
    }

    .error-message {
        display: none;
        color: red !important;
    }
</style>
<div class="card mb-4">
    <div class="card-body pb-3">
        <div class="profile-detail">
            <div class="row">

                <div class="col-lg-4 col-md-6 mb-3">
                    <label for="name" class="form-label"> {{ __('index.name') }} <span style="color: red">*</span></label>
                    <input type="text" class="form-control"
                           id="name"
                           name="name"
                           value="{{ ( isset($userDetail) ? $userDetail->name: old('name') )}}" autocomplete="off"
                           placeholder="{{ __('index.enter_name') }}" required>
                </div>


                <div class="col-lg-4 col-md-6 mb-3">
                    <label for="email" class="form-label">{{ __('index.email') }} <span style="color: red">*</span></label>
                    <input type="email" class="form-control" id="email" name="email"
                           value="{{ ( isset($userDetail) ? $userDetail->email: old('email') )}}" required
                           autocomplete="off" placeholder="{{ __('index.enter_email') }}">
                </div>

                <div class="col-lg-4 mb-3">
                    <label for="avatar" class="form-label">{{ __('index.upload_avatar') }} </label>
                    <input class="form-control"
                           type="file"
                           id="avatar"
                           name="avatar"
                           accept="image/*"
                           value="{{ isset($userDetail) ? $userDetail->avatar: old('avatar') }}">

                    <img class="mt-2 rounded {{(isset($userDetail) && $userDetail->avatar) ? '': 'd-none'}}"
                         id="image-preview"
                         src="{{ (isset($userDetail) && $userDetail->avatar) ? asset(\App\Models\Admin::AVATAR_UPLOAD_PATH.$userDetail->avatar) : ''}}"
                         style="object-fit: contain"
                         width="100"
                         height="100"
                    >
                </div>
                <div class="col-lg-4 mb-3">
                    <label for="username" class="form-label">{{ __('index.username') }} <span style="color: red">*</span></label>
                    <input type="text" class="form-control" id="username" name="username"
                        value="{{ ( isset($userDetail) ? $userDetail->username: old('username') )}}"
                        required
                        autocomplete="off" placeholder="{{ __('index.enter_username') }}">
                </div>
                @if(!isset($userDetail))
                    <div class="col-lg-4 mb-3">
                        <label for="password" class="form-label">{{ __('index.password') }} <span style="color: red">*</span></label>
                        <input type="password" class="form-control" id="password" name="password"
                            value="{{old('password')}}" autocomplete="off" placeholder="{{ __('index.enter_password') }}" required>
                    </div>
                @endif

                <!-- New Fields Section -->
                <div class="col-12 mb-3">
                    <h5 class="card-title">{{ __('index.additional_information') }}</h5>
                    <hr>
                </div>

                <!-- Bank Customer ID -->
                <div class="col-lg-4 col-md-6 mb-3">
                    <label for="bank_customer_id" class="form-label">{{ __('index.bank_customer_id') }}</label>
                    <input type="text" class="form-control" id="bank_customer_id" name="bank_customer_id"
                           value="{{ ( isset($userDetail) ? $userDetail->bank_customer_id: old('bank_customer_id') )}}"
                           autocomplete="off" placeholder="{{ __('index.enter_bank_customer_id') }}">
                </div>

                <!-- Religion -->
                <div class="col-lg-4 col-md-6 mb-3">
                    <label for="religion" class="form-label">{{ __('index.religion') }}</label>
                    <select class="form-select" id="religion" name="religion">
                        <option value="">{{ __('index.select_religion') }}</option>
                        <option value="islam" {{ (isset($userDetail) && $userDetail->religion == 'islam') ? 'selected' : '' }}>{{ __('index.islam') }}</option>
                        <option value="christianity" {{ (isset($userDetail) && $userDetail->religion == 'christianity') ? 'selected' : '' }}>{{ __('index.christianity') }}</option>
                        <option value="other" {{ (isset($userDetail) && $userDetail->religion == 'other') ? 'selected' : '' }}>{{ __('index.other') }}</option>
                    </select>
                </div>

                <!-- Team Mate -->
                <div class="col-lg-4 col-md-6 mb-3">
                    <label for="team_mate_id" class="form-label">{{ __('index.team_mate') }}</label>
                    <select class="form-select" id="team_mate_id" name="team_mate_id">
                        <option value="">{{ __('index.select_team_mate') }}</option>
                        @if(isset($employees))
                            @foreach($employees as $employee)
                                <option value="{{ $employee->id }}"
                                    {{ (isset($userDetail) && $userDetail->team_mate_id == $employee->id) ? 'selected' : '' }}>
                                    {{ $employee->name }}
                                </option>
                            @endforeach
                        @endif
                    </select>
                </div>

                <!-- Number of Spouses -->
                <div class="col-lg-4 col-md-6 mb-3">
                    <label for="number_of_spouses" class="form-label">{{ __('index.number_of_spouses') }}</label>
                    <input type="number" class="form-control" id="number_of_spouses" name="number_of_spouses"
                           value="{{ ( isset($userDetail) ? $userDetail->number_of_spouses: old('number_of_spouses') )}}"
                           min="0" max="4" autocomplete="off" placeholder="0">
                </div>

                <!-- Number of Children -->
                <div class="col-lg-4 col-md-6 mb-3">
                    <label for="number_of_children" class="form-label">{{ __('index.number_of_children') }}</label>
                    <input type="number" class="form-control" id="number_of_children" name="number_of_children"
                           value="{{ ( isset($userDetail) ? $userDetail->number_of_children: old('number_of_children') )}}"
                           min="0" max="20" autocomplete="off" placeholder="0">
                </div>

            </div>
        </div>
    </div>
</div>

<button type="submit" class="btn btn-primary">
    <i class="link-icon" data-feather="plus"></i> {{isset($userDetail)? __('index.update_user'):__('index.create_user')}}
</button>
