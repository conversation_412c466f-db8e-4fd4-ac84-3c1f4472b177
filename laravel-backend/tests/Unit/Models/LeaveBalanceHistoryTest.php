<?php

namespace Tests\Unit\Models;

use App\Models\LeaveBalanceHistory;
use App\Models\User;
use App\Models\LeaveRequestMaster;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class LeaveBalanceHistoryTest extends TestCase
{
    use RefreshDatabase;

    public function test_leave_balance_history_can_be_created()
    {
        $user = User::factory()->create();

        $history = LeaveBalanceHistory::create([
            'employee_id' => $user->id,
            'balance_before' => 10.0,
            'balance_after' => 8.5,
            'change_amount' => -1.5,
            'change_type' => 'used',
            'description' => 'Annual leave used'
        ]);

        $this->assertInstanceOf(LeaveBalanceHistory::class, $history);
        $this->assertEquals($user->id, $history->employee_id);
        $this->assertEquals(10.0, $history->balance_before);
        $this->assertEquals(8.5, $history->balance_after);
        $this->assertEquals(-1.5, $history->change_amount);
        $this->assertEquals('used', $history->change_type);
    }

    public function test_leave_balance_history_belongs_to_employee()
    {
        $user = User::factory()->create();
        $history = LeaveBalanceHistory::factory()->create(['employee_id' => $user->id]);

        $this->assertInstanceOf(User::class, $history->employee);
        $this->assertEquals($user->id, $history->employee->id);
    }

    public function test_leave_balance_history_can_belong_to_leave_request()
    {
        $user = User::factory()->create();
        $leaveRequest = LeaveRequestMaster::factory()->create(['requested_by' => $user->id]);

        $history = LeaveBalanceHistory::factory()->create([
            'employee_id' => $user->id,
            'leave_request_id' => $leaveRequest->id
        ]);

        $this->assertInstanceOf(LeaveRequestMaster::class, $history->leaveRequest);
        $this->assertEquals($leaveRequest->id, $history->leaveRequest->id);
    }

    public function test_change_types_constant_exists()
    {
        $expectedTypes = [
            'earned' => 'مكتسب',
            'used' => 'مستخدم',
            'adjusted' => 'معدل'
        ];

        $this->assertEquals($expectedTypes, LeaveBalanceHistory::CHANGE_TYPES);
    }
}
