<?php

namespace Tests\Unit\Models;

use App\Models\AdvancePaymentSetting;
use App\Models\Company;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class AdvancePaymentSettingTest extends TestCase
{
    use RefreshDatabase;

    public function test_advance_payment_setting_can_be_created()
    {
        $company = Company::factory()->create();

        $setting = AdvancePaymentSetting::create([
            'company_id' => $company->id,
            'is_enabled' => true,
            'payment_type' => 'percentage',
            'percentage' => 25.0,
            'max_amount' => 5000.0,
            'max_requests_per_month' => 2
        ]);

        $this->assertInstanceOf(AdvancePaymentSetting::class, $setting);
        $this->assertTrue($setting->is_enabled);
        $this->assertEquals('percentage', $setting->payment_type);
        $this->assertEquals(25.0, $setting->percentage);
        $this->assertEquals(5000.0, $setting->max_amount);
        $this->assertEquals(2, $setting->max_requests_per_month);
    }

    public function test_advance_payment_setting_belongs_to_company()
    {
        $company = Company::factory()->create();
        $setting = AdvancePaymentSetting::factory()->create(['company_id' => $company->id]);

        $this->assertInstanceOf(Company::class, $setting->company);
        $this->assertEquals($company->id, $setting->company->id);
    }

    public function test_payment_types_constant_exists()
    {
        $expectedTypes = [
            'fixed' => 'مبلغ ثابت',
            'percentage' => 'نسبة مئوية'
        ];

        $this->assertEquals($expectedTypes, AdvancePaymentSetting::PAYMENT_TYPES);
    }

    public function test_fixed_amount_payment_type()
    {
        $company = Company::factory()->create();

        $setting = AdvancePaymentSetting::create([
            'company_id' => $company->id,
            'is_enabled' => true,
            'payment_type' => 'fixed',
            'fixed_amount' => 1000.0,
            'max_requests_per_month' => 1
        ]);

        $this->assertEquals('fixed', $setting->payment_type);
        $this->assertEquals(1000.0, $setting->fixed_amount);
        $this->assertNull($setting->percentage);
    }
}
