<?php

namespace Tests\Feature\Features;

use App\Models\User;
use App\Models\LeaveBalanceHistory;
use Carbon\Carbon;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class LeaveSystemTest extends TestCase
{
    use RefreshDatabase;

    public function test_user_can_calculate_leave_eligibility_date()
    {
        // Test case 1: Joining on 3rd of month (before 5th)
        $user1 = User::factory()->create([
            'joining_date' => '2024-01-03'
        ]);

        $eligibilityDate1 = $user1->calculateLeaveEligibilityDate();
        $this->assertEquals('2024-07-03', $eligibilityDate1->format('Y-m-d'));

        // Test case 2: Joining on 7th of month (after 5th)
        $user2 = User::factory()->create([
            'joining_date' => '2024-01-07'
        ]);

        $eligibilityDate2 = $user2->calculateLeaveEligibilityDate();
        $this->assertEquals('2024-07-01', $eligibilityDate2->format('Y-m-d'));
    }

    public function test_user_can_calculate_annual_leave_entitlement()
    {
        // Create user who joined 8 months ago
        $joiningDate = Carbon::now()->subMonths(8)->startOfMonth();
        $user = User::factory()->create([
            'joining_date' => $joiningDate->format('Y-m-d')
        ]);

        $entitlement = $user->calculateAnnualLeaveEntitlement();

        // Should have some entitlement since they're eligible
        $this->assertGreaterThan(0, $entitlement);
    }

    public function test_user_not_eligible_for_leave_before_six_months()
    {
        // Create user who joined 3 months ago
        $joiningDate = Carbon::now()->subMonths(3);
        $user = User::factory()->create([
            'joining_date' => $joiningDate->format('Y-m-d')
        ]);

        $entitlement = $user->calculateAnnualLeaveEntitlement();

        // Should have 0 entitlement since they're not eligible yet
        $this->assertEquals(0, $entitlement);
    }

    public function test_leave_balance_history_tracks_changes()
    {
        $user = User::factory()->create();

        // Create initial balance
        $history1 = LeaveBalanceHistory::create([
            'employee_id' => $user->id,
            'balance_before' => 0,
            'balance_after' => 21,
            'change_amount' => 21,
            'change_type' => 'earned',
            'description' => 'Annual leave allocation'
        ]);

        // Use some leave
        $history2 = LeaveBalanceHistory::create([
            'employee_id' => $user->id,
            'balance_before' => 21,
            'balance_after' => 19.5,
            'change_amount' => -1.5,
            'change_type' => 'used',
            'description' => 'Sick leave used'
        ]);

        $this->assertCount(2, $user->leaveBalanceHistory);
        $this->assertEquals('earned', $history1->change_type);
        $this->assertEquals('used', $history2->change_type);
    }

    public function test_user_has_team_mate_relationship()
    {
        $teamMate = User::factory()->create();
        $user = User::factory()->create([
            'team_mate_id' => $teamMate->id
        ]);

        $this->assertInstanceOf(User::class, $user->teamMate);
        $this->assertEquals($teamMate->id, $user->teamMate->id);

        // Test reverse relationship
        $this->assertCount(1, $teamMate->teamMembers);
        $this->assertEquals($user->id, $teamMate->teamMembers->first()->id);
    }
}
