<?php
namespace App\Requests\User;


use App\Helpers\AppHelper;
use App\Models\User;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class UserUpdateRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    public function prepareForValidation()
    {
        $this->merge([
            'joining_date' => $this->input('joining_date') ? AppHelper::getEnglishDate($this->input('joining_date')) : null,
            'dob' => $this->input('dob') ? AppHelper::getEnglishDate($this->input('dob')) : null,
        ]);
        if (!auth('admin')->check() && auth()->check()) {
            $this->merge(['branch_id' => auth()->user()->branch_id]);
        }

    }
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'name' => 'required|string|max:100|min:2',
            'email' => ['required','email', Rule::unique('users')->ignore($this->employee)],
            'username' => ['required','string', Rule::unique('users')->ignore($this->employee)],
            'address' => 'nullable|required_unless:role_id,1',
            'dob' => 'nullable|required_unless:role_id,1|date|before:today',
            'phone' => 'nullable|required_unless:role_id,1|numeric',
            'gender' => ['nullable','required_unless:role_id,1', 'string', Rule::in(User::GENDER)],
            'marital_status' => ['nullable','required_unless:role_id,1', 'string', Rule::in(User::MARITAL_STATUS)],
            'employment_type' => ['nullable','required_unless:role_id,1', 'string', Rule::in(User::EMPLOYMENT_TYPE)],
            'joining_date' => 'nullable|date|before_or_equal:today',
            'role_id' => 'required|exists:roles,id',
            'branch_id' => 'nullable|required_unless:role_id,1|exists:branches,id',
            'department_id' => 'nullable|required_unless:role_id,1|exists:departments,id',
            'post_id' => 'nullable|required_unless:role_id,1|exists:posts,id',
            'supervisor_id' => 'nullable|exists:users,id',
            'office_time_id' => 'nullable|required_unless:role_id,1|exists:office_times,id',
            'leave_allocated' => 'nullable|numeric|gte:0',
            'remarks' => 'nullable|string|max:1000',
            'workspace_type' => ['nullable', 'boolean', Rule::in([1, 0])],
            'avatar' => ['sometimes', 'file', 'mimes:jpeg,png,jpg,svg','max:5048'],
            'employee_code' => ['nullable'],
            'allow_holiday_check_in' => ['nullable'],

            // Insurance fields
            'id_number' => 'nullable|string|max:50',
            'english_name' => 'nullable|string|max:255',
            'insurance_type' => 'nullable|in:health,social',
            'health_insurance_type' => 'nullable|in:public,private',
            'fingerprint_code' => 'nullable|string|max:100',
            'be_connect_code' => 'nullable|string|max:100',
            'be_connect_client_code' => 'nullable|string|max:100',
            'bank_branch_code' => 'nullable|string|max:50',
            'bank_customer_id' => 'nullable|string|max:50',
            'number_of_spouses' => 'nullable|integer|min:0|max:4',
            'number_of_children' => 'nullable|integer|min:0|max:20',
            'team_mate_id' => 'nullable|exists:users,id',
            'religion' => 'nullable|in:islam,christianity,other',
            'emergency_contact' => 'nullable|string|max:20',
        ];

    }

    public function messages()
    {
        return [
            'required_unless' => 'The :attribute field is required',
        ];
    }

    public function attributes()
    {
        return [
            'dob' => 'date of birth',
            'phone' => 'phone number',
            'gender' => 'gender',
            'marital_status' => 'marital status',
            'employment_type' => 'employment type',
            'branch_id' => 'branch',
            'department_id' => 'department',
            'post_id' => 'post',
            'office_time_id' => 'office time',
        ];
    }

}















