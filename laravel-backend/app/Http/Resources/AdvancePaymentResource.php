<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class AdvancePaymentResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'requested_amount' => $this->requested_amount,
            'approved_amount' => $this->approved_amount,
            'status' => $this->status,
            'status_name' => $this->status ? (self::STATUS[$this->status] ?? $this->status) : null,
            'reason' => $this->reason,
            'admin_notes' => $this->admin_notes,
            'request_date' => $this->request_date,
            'approved_date' => $this->approved_date,
            'payment_date' => $this->payment_date,
            'approved_by' => $this->whenLoaded('approvedBy', function() {
                return [
                    'id' => $this->approvedBy->id,
                    'name' => $this->approvedBy->name
                ];
            }),
            'processed_by' => $this->whenLoaded('processedBy', function() {
                return [
                    'id' => $this->processedBy->id,
                    'name' => $this->processedBy->name
                ];
            }),
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at
        ];
    }
}
