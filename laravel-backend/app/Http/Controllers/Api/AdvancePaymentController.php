<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\AdvancePaymentSetting;
use App\Models\EmployeeAdvancePayment;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Validator;

class AdvancePaymentController extends Controller
{
    /**
     * Display advance payment settings for company
     */
    public function getSettings(): JsonResponse
    {
        try {
            $user = Auth::user();
            $settings = AdvancePaymentSetting::where('company_id', $user->company_id)
                ->where('is_enabled', true)
                ->first();

            if (!$settings) {
                return response()->json([
                    'success' => false,
                    'message' => 'Advance payment not enabled for your company'
                ], 404);
            }

            return response()->json([
                'success' => true,
                'data' => $settings
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get employee's advance payment requests
     */
    public function index(): JsonResponse
    {
        try {
            $user = Auth::user();
            $requests = EmployeeAdvancePayment::where('employee_id', $user->id)
                ->with(['approvedBy', 'processedBy'])
                ->orderBy('created_at', 'desc')
                ->get();

            return response()->json([
                'success' => true,
                'data' => $requests
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Create new advance payment request
     */
    public function store(Request $request): JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'requested_amount' => 'required|numeric|min:1',
                'reason' => 'required|string|max:500'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Validation failed',
                    'errors' => $validator->errors()
                ], 422);
            }

            $user = Auth::user();

            // Check if advance payment is enabled
            $settings = AdvancePaymentSetting::where('company_id', $user->company_id)
                ->where('is_enabled', true)
                ->first();

            if (!$settings) {
                return response()->json([
                    'success' => false,
                    'message' => 'Advance payment not enabled'
                ], 403);
            }

            // Check monthly limit
            $currentMonthRequests = EmployeeAdvancePayment::where('employee_id', $user->id)
                ->whereMonth('request_date', now()->month)
                ->whereYear('request_date', now()->year)
                ->count();

            if ($currentMonthRequests >= $settings->max_requests_per_month) {
                return response()->json([
                    'success' => false,
                    'message' => 'Monthly request limit exceeded'
                ], 403);
            }

            $advancePayment = EmployeeAdvancePayment::create([
                'employee_id' => $user->id,
                'company_id' => $user->company_id,
                'requested_amount' => $request->requested_amount,
                'reason' => $request->reason,
                'request_date' => now()->toDateString(),
                'status' => 'pending'
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Advance payment request submitted successfully',
                'data' => $advancePayment
            ], 201);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id): JsonResponse
    {
        try {
            $user = Auth::user();
            $request = EmployeeAdvancePayment::where('id', $id)
                ->where('employee_id', $user->id)
                ->with(['approvedBy', 'processedBy'])
                ->first();

            if (!$request) {
                return response()->json([
                    'success' => false,
                    'message' => 'Request not found'
                ], 404);
            }

            return response()->json([
                'success' => true,
                'data' => $request
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage()
            ], 500);
        }
    }
}
