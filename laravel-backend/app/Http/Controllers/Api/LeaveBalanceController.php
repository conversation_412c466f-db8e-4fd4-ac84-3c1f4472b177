<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\LeaveBalanceHistory;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Auth;

class LeaveBalanceController extends Controller
{
    /**
     * Get employee's leave balance and history
     */
    public function index(): JsonResponse
    {
        try {
            $user = Auth::user();

            $leaveData = [
                'current_balance' => $user->accumulated_leave_balance ?? 0,
                'leave_allocated' => $user->leave_allocated ?? 0,
                'leave_eligibility_date' => $user->leave_eligibility_date,
                'is_eligible' => $user->leave_eligibility_date && $user->leave_eligibility_date <= now(),
                'annual_entitlement' => $user->calculateAnnualLeaveEntitlement(),
                'history' => $user->leaveBalanceHistory()->orderBy('created_at', 'desc')->get()
            ];

            return response()->json([
                'success' => true,
                'data' => $leaveData
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get leave balance history
     */
    public function history(): JsonResponse
    {
        try {
            $user = Auth::user();
            $history = LeaveBalanceHistory::where('employee_id', $user->id)
                ->with('leaveRequest')
                ->orderBy('created_at', 'desc')
                ->paginate(20);

            return response()->json([
                'success' => true,
                'data' => $history
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Calculate leave eligibility for employee
     */
    public function calculateEligibility(): JsonResponse
    {
        try {
            $user = Auth::user();

            $eligibilityDate = $user->calculateLeaveEligibilityDate();
            $annualEntitlement = $user->calculateAnnualLeaveEntitlement();

            $data = [
                'joining_date' => $user->joining_date,
                'leave_eligibility_date' => $eligibilityDate,
                'is_eligible' => $eligibilityDate && $eligibilityDate <= now(),
                'annual_entitlement' => $annualEntitlement,
                'months_until_eligible' => $eligibilityDate && $eligibilityDate > now()
                    ? now()->diffInMonths($eligibilityDate)
                    : 0
            ];

            return response()->json([
                'success' => true,
                'data' => $data
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get team mate information
     */
    public function teamMate(): JsonResponse
    {
        try {
            $user = Auth::user();

            $teamMateData = [
                'team_mate' => $user->teamMate ? [
                    'id' => $user->teamMate->id,
                    'name' => $user->teamMate->name,
                    'email' => $user->teamMate->email,
                    'department' => $user->teamMate->department->dept_name ?? null,
                    'branch' => $user->teamMate->branch->name ?? null
                ] : null,
                'team_members' => $user->teamMembers->map(function($member) {
                    return [
                        'id' => $member->id,
                        'name' => $member->name,
                        'email' => $member->email,
                        'department' => $member->department->dept_name ?? null,
                        'branch' => $member->branch->name ?? null
                    ];
                })
            ];

            return response()->json([
                'success' => true,
                'data' => $teamMateData
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage()
            ], 500);
        }
    }
}
