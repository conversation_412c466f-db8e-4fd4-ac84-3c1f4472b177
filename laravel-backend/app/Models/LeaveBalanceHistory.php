<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class LeaveBalanceHistory extends Model
{
    use HasFactory;

    protected $table = 'leave_balance_history';

    protected $fillable = [
        'employee_id',
        'balance_before',
        'balance_after',
        'change_amount',
        'change_type',
        'description',
        'leave_request_id'
    ];

    const CHANGE_TYPES = [
        'earned' => 'مكتسب',
        'used' => 'مستخدم',
        'adjusted' => 'معدل'
    ];

    public function employee(): BelongsTo
    {
        return $this->belongsTo(User::class, 'employee_id', 'id');
    }

    public function leaveRequest(): BelongsTo
    {
        return $this->belongsTo(LeaveRequestMaster::class, 'leave_request_id', 'id');
    }
}
