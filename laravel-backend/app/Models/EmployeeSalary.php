<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Auth;

class EmployeeSalary extends Model
{
    use HasFactory;

    protected $table = 'employee_salaries';

    protected $fillable = ['employee_id', 'annual_salary', 'basic_salary_type', 'basic_salary_value', 'salary_type', 'calculated_hourly_rate', 'monthly_basic_salary', 'annual_basic_salary',
        'monthly_fixed_allowance', 'annual_fixed_allowance', 'salary_group_id','hour_rate','weekly_basic_salary','weekly_fixed_allowance'];


    public static function boot()
    {
        parent::boot();

        if (Auth::check()  && isset(Auth::user()->branch_id)) {
            $user = Auth::user();

            static::addGlobalScope('branch', function (Builder $builder) use($user){
                $branchId = $user->branch_id;
                $builder->whereHas('employee', function ($query) use ($branchId) {
                    $query->where('branch_id', $branchId);
                });

            });
        }
    }

    const SALARY_TYPES = [
        'monthly' => 'شهري',
        'daily' => 'يومي',
        'hourly' => 'بالساعة',
        'fixed' => 'راتب ثابت'
    ];

    public function employee()
    {
        return $this->belongsTo(User::class, 'employee_id', 'id');
    }

    /**
     * Calculate hourly rate based on salary type
     */
    public function calculateHourlyRate()
    {
        switch ($this->salary_type) {
            case 'hourly':
                return $this->hour_rate ?? 0;
            case 'daily':
                return ($this->basic_salary_value ?? 0) / 8; // Assuming 8 hours per day
            case 'monthly':
                return ($this->monthly_basic_salary ?? 0) / (30 * 8); // Assuming 30 days, 8 hours per day
            case 'fixed':
                return ($this->annual_salary ?? 0) / (365 * 8); // Assuming 365 days, 8 hours per day
            default:
                return 0;
        }
    }

    /**
     * Update calculated hourly rate
     */
    public function updateCalculatedHourlyRate()
    {
        $this->calculated_hourly_rate = $this->calculateHourlyRate();
        $this->save();
    }
}
