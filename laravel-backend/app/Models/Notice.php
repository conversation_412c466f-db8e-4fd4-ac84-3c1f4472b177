<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Auth;

class Notice extends Model
{
    use HasFactory;

    protected $table = 'notices';

    protected $fillable = [
        'title',
        'description',
        'image',
        'notice_publish_date',
        'company_id',
        'branch_id',
        'target_departments',
        'target_religions',
        'send_to_all_in_branch',
        'is_active',
        'created_by',
        'updated_by',
    ];

    protected $casts = [
        'target_departments' => 'array',
        'target_religions' => 'array',
        'send_to_all_in_branch' => 'boolean',
        'is_active' => 'boolean',
        'notice_publish_date' => 'datetime'
    ];

    const RECORDS_PER_PAGE = 20;

    public static function boot()
    {
        parent::boot();

        static::creating(function ($model) {
            $model->created_by = Auth::user()->id ?? null;
            $model->notice_publish_date = Carbon::now()->format('Y-m-d H:i:s');
        });

        static::updating(function ($model) {
            $model->updated_by = Auth::user()->id ?? null;
            $model->notice_publish_date = Carbon::now()->format('Y-m-d H:i:s');
        });

        static::deleting(function ($noticeDetail) {
            $noticeDetail->noticeReceiversDetail()->delete();
        });

        if (Auth::check()  && isset(Auth::user()->branch_id)) {
            $user = Auth::user();

            static::addGlobalScope('branch', function (Builder $builder) use ($user) {
                $branchId = $user->branch_id;
                 $builder->whereHas('branch', function ($query) use ($branchId) {
                    $query->where('id', $branchId);
                });
            });
        }


    }

    public function createdBy()
    {
        return $this->belongsTo(User::class, 'created_by', 'id');
    }

    public function updatedBy()
    {
        return $this->belongsTo(User::class, 'updated_by', 'id');
    }

    public function company()
    {
        return $this->belongsTo(Company::class, 'company_id', 'id');
    }

    public function noticeReceiversDetail()
    {
        return $this->hasMany(NoticeReceiver::class, 'notice_id', 'id')->whereHas('employee');
    }

    public function branch(): BelongsTo
    {
        return $this->belongsTo(Branch::class, 'branch_id', 'id');
    }

    /**
     * Get eligible users in the branch based on filters
     */
    public function getEligibleUsersInBranch()
    {
        $query = User::query()
            ->where('is_active', true)
            ->where('branch_id', $this->branch_id);

        // If not send to all in branch, apply filters
        if (!$this->send_to_all_in_branch) {
            // Filter by departments
            if (!empty($this->target_departments)) {
                $query->whereIn('department_id', $this->target_departments);
            }

            // Filter by religions
            if (!empty($this->target_religions)) {
                $query->whereIn('religion', $this->target_religions);
            }
        }

        return $query->get();
    }

}
