<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class EmployeeAdvancePayment extends Model
{
    use HasFactory;

    protected $table = 'employee_advance_payments';

    protected $fillable = [
        'employee_id',
        'company_id',
        'requested_amount',
        'approved_amount',
        'status',
        'reason',
        'admin_notes',
        'request_date',
        'approved_date',
        'payment_date',
        'approved_by',
        'processed_by'
    ];

    const STATUS = [
        'pending' => 'في الانتظار',
        'approved' => 'موافق عليه',
        'rejected' => 'مرفوض',
        'paid' => 'مدفوع'
    ];

    public function employee(): BelongsTo
    {
        return $this->belongsTo(User::class, 'employee_id', 'id');
    }

    public function company(): BelongsTo
    {
        return $this->belongsTo(Company::class, 'company_id', 'id');
    }

    public function approvedBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'approved_by', 'id');
    }

    public function processedBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'processed_by', 'id');
    }
}
