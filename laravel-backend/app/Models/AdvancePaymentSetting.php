<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class AdvancePaymentSetting extends Model
{
    use HasFactory;

    protected $table = 'advance_payment_settings';

    protected $fillable = [
        'company_id',
        'is_enabled',
        'payment_type',
        'fixed_amount',
        'percentage',
        'max_amount',
        'max_requests_per_month'
    ];

    const PAYMENT_TYPES = [
        'fixed' => 'مبلغ ثابت',
        'percentage' => 'نسبة مئوية'
    ];

    public function company(): BelongsTo
    {
        return $this->belongsTo(Company::class, 'company_id', 'id');
    }
}
