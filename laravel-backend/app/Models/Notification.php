<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Auth;

class Notification extends Model
{
    use HasFactory;

    const RECORDS_PER_PAGE = 20;

    const TYPES = [
        'general',
        'comment',
        'project',
        'task',
        'attendance',
        'leave',
        'support',
        'tada',
        'holiday',
        'payroll',
        'resignation',
        'termination'
    ];

    protected $table = 'notifications';

    protected $fillable = [
        'title',
        'description',
        'image',
        'type',
        'notification_for_id',
        'notification_publish_date',
        'company_id',
        'target_branches',
        'target_departments',
        'target_religions',
        'send_to_all',
        'is_active',
        'created_by',
        'updated_by',
    ];

    protected $casts = [
        'target_branches' => 'array',
        'target_departments' => 'array',
        'target_religions' => 'array',
        'send_to_all' => 'boolean',
        'is_active' => 'boolean',
        'notification_publish_date' => 'datetime'
    ];

    public static function boot()
    {
        parent::boot();

        static::creating(function ($model) {
            $model->created_by = Auth::user()->id ?? null;
            $model->notification_publish_date = Carbon::now()->format('Y-m-d H:i:s');
        });

        static::updating(function ($model) {
            $model->updated_by = Auth::user()->id ?? null;
            $model->notification_publish_date = Carbon::now()->format('Y-m-d H:i:s');
        });

        static::deleting(function($notifiedUserDetail){
            $notifiedUserDetail->notifiedUsers()->delete();
        });

        if (Auth::check()  && isset(Auth::user()->branch_id)) {
            $branchId = Auth::user()->branch_id;

            static::addGlobalScope('branch', function (Builder $builder) use($branchId){
                $builder->whereHas('createdBy', function ($query) use ($branchId) {
                        $query->where('branch_id', $branchId);
                    });

            });
        }

    }

    public function createdBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'created_by', 'id');
    }

    public function updatedBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'updated_by', 'id');
    }

    public function company(): BelongsTo
    {
        return $this->belongsTo(Company::class, 'company_id', 'id');
    }

    public function notifiedUsers(): HasMany
    {
        return $this->hasMany(UserNotification::class, 'notification_id', 'id');
    }

    public function recipients(): HasMany
    {
        return $this->hasMany(NotificationRecipient::class, 'notification_id', 'id');
    }

    /**
     * Get eligible users based on filters
     */
    public function getEligibleUsers()
    {
        $query = User::query()->where('is_active', true);

        // If not send to all, apply filters
        if (!$this->send_to_all) {
            // Filter by branches
            if (!empty($this->target_branches)) {
                $query->whereIn('branch_id', $this->target_branches);
            }

            // Filter by departments
            if (!empty($this->target_departments)) {
                $query->whereIn('department_id', $this->target_departments);
            }

            // Filter by religions
            if (!empty($this->target_religions)) {
                $query->whereIn('religion', $this->target_religions);
            }
        }

        return $query->get();
    }

}
