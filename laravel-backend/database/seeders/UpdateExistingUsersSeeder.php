<?php

namespace Database\Seeders;

use App\Models\User;
use App\Models\Branch;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class UpdateExistingUsersSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Update existing users with new fields
        $users = User::all();
        $religions = ['islam', 'christianity', 'other'];

        foreach ($users as $index => $user) {
            // Add sample data for new fields
            $user->update([
                'bank_customer_id' => 'BC' . str_pad($user->id, 6, '0', STR_PAD_LEFT),
                'religion' => $religions[$index % 3],
                'number_of_spouses' => rand(0, 2),
                'number_of_children' => rand(0, 5),
                'leave_eligibility_date' => $user->calculateLeaveEligibilityDate(),
                'accumulated_leave_balance' => $user->calculateAnnualLeaveEntitlement(),
                'salary_set' => true
            ]);
        }

        // Update branches with landline phones
        $branches = Branch::all();
        foreach ($branches as $branch) {
            $branch->update([
                'landline_phone' => '02-' . rand(********, ********)
            ]);
        }

        // Set some users as team mates
        $userIds = $users->pluck('id')->toArray();
        foreach ($users->take(10) as $user) {
            $availableTeamMates = array_diff($userIds, [$user->id]);
            if (!empty($availableTeamMates)) {
                $user->update([
                    'team_mate_id' => $availableTeamMates[array_rand($availableTeamMates)]
                ]);
            }
        }
    }
}
