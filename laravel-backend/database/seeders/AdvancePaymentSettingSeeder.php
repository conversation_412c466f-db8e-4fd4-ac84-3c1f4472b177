<?php

namespace Database\Seeders;

use App\Models\AdvancePaymentSetting;
use App\Models\Company;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class AdvancePaymentSettingSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $companies = Company::all();

        foreach ($companies as $company) {
            // Create percentage-based advance payment setting
            AdvancePaymentSetting::create([
                'company_id' => $company->id,
                'is_enabled' => true,
                'payment_type' => 'percentage',
                'percentage' => 25.0,
                'max_amount' => 5000.0,
                'max_requests_per_month' => 2
            ]);

            // Create fixed amount advance payment setting for testing
            AdvancePaymentSetting::create([
                'company_id' => $company->id,
                'is_enabled' => false,
                'payment_type' => 'fixed',
                'fixed_amount' => 1000.0,
                'max_requests_per_month' => 1
            ]);
        }
    }
}
