<?php

namespace Database\Seeders;

use App\Models\Notification;
use App\Models\NotificationRecipient;
use App\Models\User;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class NotificationRecipientSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create sample notifications with enhanced filtering
        $notification1 = Notification::create([
            'title' => 'إشعار عام لجميع الموظفين',
            'description' => 'هذا إشعار عام لجميع موظفي الشركة',
            'type' => 'general',
            'notification_publish_date' => now(),
            'company_id' => 1,
            'send_to_all' => true,
            'is_active' => true,
            'created_by' => 1,
            'updated_by' => 1
        ]);

        $notification2 = Notification::create([
            'title' => 'إشعار للمسلمين فقط',
            'description' => 'إشعار خاص بالموظفين المسلمين',
            'type' => 'religious',
            'notification_publish_date' => now(),
            'company_id' => 1,
            'target_religions' => ['islam'],
            'send_to_all' => false,
            'is_active' => true,
            'created_by' => 1,
            'updated_by' => 1
        ]);

        // Create recipients for notifications
        $users = User::where('is_active', true)->take(10)->get();

        foreach ($users as $user) {
            // All users receive general notification
            NotificationRecipient::create([
                'notification_id' => $notification1->id,
                'user_id' => $user->id,
                'is_read' => false
            ]);

            // Only Muslim users receive religious notification
            if ($user->religion === 'islam') {
                NotificationRecipient::create([
                    'notification_id' => $notification2->id,
                    'user_id' => $user->id,
                    'is_read' => false
                ]);
            }
        }
    }
}
