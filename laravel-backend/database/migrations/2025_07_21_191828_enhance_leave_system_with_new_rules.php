<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Enhance users table for leave management
        Schema::table('users', function (Blueprint $table) {
            $table->date('leave_eligibility_date')->nullable()->after('joining_date')->comment('تاريخ استحقاق الإجازة (بعد 6 أشهر)');
            $table->decimal('accumulated_leave_balance', 8, 2)->default(0)->after('leave_allocated')->comment('رصيد الإجازات التراكمي');
            $table->boolean('leave_notification_sent')->default(false)->after('accumulated_leave_balance')->comment('تم إرسال إشعار استحقاق الإجازة');
        });

        // Enhance leave_requests_master table
        Schema::table('leave_requests_master', function (Blueprint $table) {
            $table->boolean('is_hourly_leave')->default(false)->after('early_exit')->comment('إجازة بالساعة');
            $table->decimal('hours_requested', 5, 2)->nullable()->after('is_hourly_leave')->comment('عدد الساعات المطلوبة');
            $table->date('cancellation_deadline')->nullable()->after('hours_requested')->comment('آخر موعد للإلغاء');
            $table->boolean('is_privileged_user')->default(false)->after('cancellation_deadline')->comment('مستخدم مميز يمكنه تعديل التاريخ');
        });

        // Create leave balance history table
        Schema::create('leave_balance_history', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('employee_id');
            $table->decimal('balance_before', 8, 2);
            $table->decimal('balance_after', 8, 2);
            $table->decimal('change_amount', 8, 2);
            $table->string('change_type'); // 'earned', 'used', 'adjusted'
            $table->string('description')->nullable();
            $table->unsignedBigInteger('leave_request_id')->nullable();
            $table->timestamps();

            $table->foreign('employee_id')->references('id')->on('users')->onDelete('cascade');
            $table->foreign('leave_request_id')->references('id')->on('leave_requests_master')->onDelete('set null');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('leave_balance_history');

        Schema::table('leave_requests_master', function (Blueprint $table) {
            $table->dropColumn([
                'is_hourly_leave',
                'hours_requested',
                'cancellation_deadline',
                'is_privileged_user'
            ]);
        });

        Schema::table('users', function (Blueprint $table) {
            $table->dropColumn([
                'leave_eligibility_date',
                'accumulated_leave_balance',
                'leave_notification_sent'
            ]);
        });
    }
};
