<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Add constraint to ensure every employee has a salary record
        Schema::table('users', function (Blueprint $table) {
            // Add a flag to track if salary is set
            $table->boolean('salary_set')->default(false)->after('employee_code')->comment('هل تم تعيين الراتب');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('users', function (Blueprint $table) {
            $table->dropColumn('salary_set');
        });
    }
};
