<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Enhance notifications table with filtering options
        Schema::table('notifications', function (Blueprint $table) {
            $table->json('target_branches')->nullable()->after('company_id')->comment('الفروع المستهدفة');
            $table->json('target_departments')->nullable()->after('target_branches')->comment('الأقسام المستهدفة');
            $table->json('target_religions')->nullable()->after('target_departments')->comment('الديانات المستهدفة');
            $table->boolean('send_to_all')->default(true)->after('target_religions')->comment('إرسال للجميع');
        });

        // Enhance notices table with filtering options
        Schema::table('notices', function (Blueprint $table) {
            $table->json('target_departments')->nullable()->after('branch_id')->comment('الأقسام المستهدفة');
            $table->json('target_religions')->nullable()->after('target_departments')->comment('الديانات المستهدفة');
            $table->boolean('send_to_all_in_branch')->default(true)->after('target_religions')->comment('إرسال لجميع موظفي الفرع');
        });

        // Create notification recipients table for better tracking
        Schema::create('notification_recipients', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('notification_id');
            $table->unsignedBigInteger('user_id');
            $table->boolean('is_read')->default(false);
            $table->timestamp('read_at')->nullable();
            $table->timestamps();

            $table->foreign('notification_id')->references('id')->on('notifications')->onDelete('cascade');
            $table->foreign('user_id')->references('id')->on('users')->onDelete('cascade');
            $table->unique(['notification_id', 'user_id']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('notification_recipients');

        Schema::table('notices', function (Blueprint $table) {
            $table->dropColumn([
                'target_departments',
                'target_religions',
                'send_to_all_in_branch'
            ]);
        });

        Schema::table('notifications', function (Blueprint $table) {
            $table->dropColumn([
                'target_branches',
                'target_departments',
                'target_religions',
                'send_to_all'
            ]);
        });
    }
};
