<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Add landline phone to branches table
        Schema::table('branches', function (Blueprint $table) {
            $table->string('landline_phone')->nullable()->after('phone')->comment('رقم الهاتف الأرضي');
        });

        Schema::table('users', function (Blueprint $table) {
            // Additional banking information
            $table->string('bank_customer_id')->nullable()->after('bank_branch_code')->comment('رقم حساب العميل البنكي');

            // Family information
            $table->integer('number_of_spouses')->default(0)->after('marital_status')->comment('عدد الزوجات');
            $table->integer('number_of_children')->default(0)->after('number_of_spouses')->comment('عدد الأطفال');

            // Team mate relationship
            $table->unsignedBigInteger('team_mate_id')->nullable()->after('supervisor_id')->comment('زميل العمل');
            $table->foreign('team_mate_id')->references('id')->on('users')->onDelete('set null');

            // Religious information
            $table->string('religion')->nullable()->after('marital_status')->comment('الديانة');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('branches', function (Blueprint $table) {
            $table->dropColumn('landline_phone');
        });

        Schema::table('users', function (Blueprint $table) {
            $table->dropForeign(['team_mate_id']);
            $table->dropColumn([
                'bank_customer_id',
                'number_of_spouses',
                'number_of_children',
                'team_mate_id',
                'religion'
            ]);
        });
    }
};
