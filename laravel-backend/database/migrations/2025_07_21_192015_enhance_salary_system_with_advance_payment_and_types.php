<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Enhance employee_salaries table
        Schema::table('employee_salaries', function (Blueprint $table) {
            $table->enum('salary_type', ['monthly', 'daily', 'hourly', 'fixed'])->default('monthly')->after('basic_salary_value')->comment('نوع الراتب');
            $table->decimal('calculated_hourly_rate', 8, 2)->nullable()->after('salary_type')->comment('سعر الساعة المحسوب');
        });

        // Create advance payment settings table
        Schema::create('advance_payment_settings', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('company_id');
            $table->boolean('is_enabled')->default(false)->comment('تفعيل الدفع مقدماً');
            $table->enum('payment_type', ['fixed', 'percentage'])->default('percentage')->comment('نوع الدفع: ثابت أو نسبة');
            $table->decimal('fixed_amount', 10, 2)->nullable()->comment('المبلغ الثابت');
            $table->decimal('percentage', 5, 2)->nullable()->comment('النسبة المئوية');
            $table->decimal('max_amount', 10, 2)->nullable()->comment('الحد الأقصى للمبلغ');
            $table->integer('max_requests_per_month')->default(1)->comment('عدد الطلبات المسموحة شهرياً');
            $table->timestamps();

            $table->foreign('company_id')->references('id')->on('companies')->onDelete('cascade');
        });

        // Create employee advance payment requests table
        Schema::create('employee_advance_payments', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('employee_id');
            $table->unsignedBigInteger('company_id');
            $table->decimal('requested_amount', 10, 2);
            $table->decimal('approved_amount', 10, 2)->nullable();
            $table->enum('status', ['pending', 'approved', 'rejected', 'paid'])->default('pending');
            $table->text('reason')->nullable();
            $table->text('admin_notes')->nullable();
            $table->date('request_date');
            $table->date('approved_date')->nullable();
            $table->date('payment_date')->nullable();
            $table->unsignedBigInteger('approved_by')->nullable();
            $table->unsignedBigInteger('processed_by')->nullable();
            $table->timestamps();

            $table->foreign('employee_id')->references('id')->on('users')->onDelete('cascade');
            $table->foreign('company_id')->references('id')->on('companies')->onDelete('cascade');
            $table->foreign('approved_by')->references('id')->on('users')->onDelete('set null');
            $table->foreign('processed_by')->references('id')->on('users')->onDelete('set null');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('employee_advance_payments');
        Schema::dropIfExists('advance_payment_settings');

        Schema::table('employee_salaries', function (Blueprint $table) {
            $table->dropColumn(['salary_type', 'calculated_hourly_rate']);
        });
    }
};
