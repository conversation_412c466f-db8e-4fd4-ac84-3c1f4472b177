<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Add image field to company_content_management table
        Schema::table('company_content_management', function (Blueprint $table) {
            $table->string('image')->nullable()->after('description')->comment('صورة المحتوى');
        });

        // Add image field to notices table
        Schema::table('notices', function (Blueprint $table) {
            $table->string('image')->nullable()->after('description')->comment('صورة الإشعار');
        });

        // Add image field to notifications table
        Schema::table('notifications', function (Blueprint $table) {
            $table->string('image')->nullable()->after('description')->comment('صورة التنبيه');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('notifications', function (Blueprint $table) {
            $table->dropColumn('image');
        });

        Schema::table('notices', function (Blueprint $table) {
            $table->dropColumn('image');
        });

        Schema::table('company_content_management', function (Blueprint $table) {
            $table->dropColumn('image');
        });
    }
};
