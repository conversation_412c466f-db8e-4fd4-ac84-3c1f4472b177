<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('departments', function (Blueprint $table) {
            // Remove address and phone columns
            $table->dropColumn(['address', 'phone']);

            // Add manager approval field for employee assignment
            $table->boolean('requires_manager_approval')->default(true)->after('dept_head_id')->comment('يتطلب موافقة المدير عند تعيين موظف');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('departments', function (Blueprint $table) {
            // Add back address and phone columns
            $table->string('address')->nullable()->after('slug');
            $table->string('phone')->nullable()->after('address');

            // Remove manager approval field
            $table->dropColumn('requires_manager_approval');
        });
    }
};
